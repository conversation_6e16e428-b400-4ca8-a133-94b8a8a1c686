from django.db import models
from Authentication.models import UserRegistration, Brands


class LineAuth(models.Model):
    """Model to store LINE OAuth authentication data"""
    brand = models.ForeignKey(Brands, on_delete=models.CASCADE, blank=True, null=True)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    line_user_id = models.CharField(max_length=500, unique=True)
    display_name = models.CharField(max_length=500, default='')
    picture_url = models.CharField(max_length=1000, default='')
    status_message = models.TextField(default='', blank=True, null=True)
    access_token = models.CharField(max_length=1500, default='')
    refresh_token = models.CharField(max_length=1500, default='')
    token_expires_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'line_auth'
        unique_together = ['brand', 'line_user_id']

    def __str__(self):
        return f"{self.display_name} ({self.line_user_id})"


class LineChat(models.Model):
    """Model to store LINE chat conversations"""
    line_auth = models.ForeignKey(LineAuth, on_delete=models.CASCADE, related_name='chats')
    chat_id = models.CharField(max_length=500)
    chat_type = models.CharField(max_length=50, choices=[
        ('user', 'User'),
        ('group', 'Group'),
        ('room', 'Room')
    ], default='user')
    chat_name = models.CharField(max_length=500, default='')
    chat_picture_url = models.CharField(max_length=1000, default='')
    last_message_id = models.CharField(max_length=500, default='')
    last_message_text = models.TextField(default='')
    last_message_timestamp = models.DateTimeField(null=True, blank=True)
    unread_count = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'line_chat'
        unique_together = ['line_auth', 'chat_id']

    def __str__(self):
        return f"{self.chat_name} ({self.chat_type})"


class LineMessage(models.Model):
    """Model to store LINE messages"""
    line_chat = models.ForeignKey(LineChat, on_delete=models.CASCADE, related_name='messages')
    message_id = models.CharField(max_length=500, unique=True)
    message_type = models.CharField(max_length=50, choices=[
        ('text', 'Text'),
        ('image', 'Image'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('file', 'File'),
        ('location', 'Location'),
        ('sticker', 'Sticker'),
        ('template', 'Template'),
        ('flex', 'Flex'),
        ('postback', 'Postback')
    ], default='text')
    sender_id = models.CharField(max_length=500)
    sender_name = models.CharField(max_length=500, default='')
    sender_picture_url = models.CharField(max_length=1000, default='')
    message_text = models.TextField(default='')
    message_data = models.JSONField(default=dict, blank=True)  # Store rich message data
    timestamp = models.DateTimeField()
    is_from_user = models.BooleanField(default=True)  # True if from user, False if from bot
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'line_message'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.sender_name}: {self.message_text[:50]}..."


class LineWebhookEvent(models.Model):
    """Model to store LINE webhook events for debugging and processing"""
    event_type = models.CharField(max_length=100)
    source_type = models.CharField(max_length=50)
    source_id = models.CharField(max_length=500)
    user_id = models.CharField(max_length=500, default='')
    event_data = models.JSONField()
    processed = models.BooleanField(default=False)
    processed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(default='', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'line_webhook_event'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.event_type} - {self.source_type} ({self.created_at})"
